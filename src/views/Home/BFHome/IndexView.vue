<template>
  <div class="bf-home">
    <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirect-to-search="true" redirect-url="/search"
      @search="handleSearch" />

    <!-- Banner轮播 -->
    <div class="banner-container" v-if="headerBannerList.length > 0">
      <GoodsImageSwiper :media-list="headerBannerList" mode="banner" paginationType="fraction" :autoplay="true"
        :loop="true" height="200px" @image-click="handleBannerClick" />
    </div>

    <!-- 宫格菜单 -->
    <div class="grid-menu-container" v-if="gridMenuItems.length > 0">
      <GridMenu :items="gridMenuItems" :columns="5" :show-more="true" :max-items="10" @item-click="handleGridItemClick"
        @more-click="handleMoreClick" />
    </div>


    <Block title="爆款好物" subtitle="精选优质商品，限时特惠" @goods-click="handleGoodsClick">
      <van-list v-if="guessList.length > 0" :loading="loading" :finished="finished" finished-text="没有更多了"
        @load="handleLoadMore">
        <Waterfall :list="guessList" :breakpoints="breakpoints" :hasAroundGutter="false">
          <template #default="{ item }">
            <GoodsCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
          </template>
        </Waterfall>
      </van-list>
    </Block>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import SearchHeader from '@components/Common/SearchHeader.vue'
import GoodsImageSwiper from '@/components/Common/GoodsImageSwiper.vue'
import GridMenu from '@components/Common/Home/GridMenu.vue'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import Block from '@components/Common/Home/Block.vue'
import GoodsCard from '@components/Common/Home/GoodsCard.vue'
import { fenToYuan } from '@utils/amount.js'
import { useRouter } from 'vue-router'
const router = useRouter()
// 基础数据
const searchKeyword = ref('')
const headerBannerList = ref([])

// 10宫格菜单数据
const gridMenuItems = ref([])

// 爆款好物数据
const guessList = ref([])
const loading = ref(false)
const finished = ref(false)
const bfhwCurrentPage = ref(1)
const bffhwPageSize = ref(10)
const isInitialLoad = ref(true) // 标记是否为初始加载

// 瀑布流配置
const breakpoints = ref({
  750: { rowPerView: 2 },
  550: { rowPerView: 2 },
  375: { rowPerView: 2 },
  290: { rowPerView: 1 }
})

// banner 过滤对应渠道的数据
const channelFilterd = list => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}

// 获取头部banner列表
const getHeaderBannerList = async () => {
  const [err, json] = await getBannerInfo({ bizCode: getBizCode('QUERY'), showPage: 1 })
  console.warn(213132, err, json)
  if (!err) {
    // 转换数据格式为GoodsImageSwiper组件需要的格式
    const bannerData = channelFilterd(json).map(item => ({
      type: 'image',
      url: item.imgUrl,
      alt: item.bannerChName,
      linkUrl: item.url,
    }))
    console.warn(1231323123, bannerData)
    headerBannerList.value = bannerData
  }
}

// 获取宫格菜单列表
const getIconList = async () => {
  const [err, json] = await getIconInfo({
    bizCode: getBizCode('QUERY'),
    channel: curChannelBiz.get(),
    showPage: 2
  })

  if (!err) {
    if (json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData.slice(0, 4)
    } else {
      gridMenuItems.value = []
    }
  }

}

// 搜索处理
const handleSearch = () => {
  // 搜索功能由 SearchHeader 组件处理
}

// Banner点击处理
const handleBannerClick = ({ item }) => {
  if (item.linkUrl) {
    // 处理banner点击跳转逻辑
    window.location.href = item.linkUrl
  }
}

// 宫格菜单点击处理
const handleGridItemClick = ({ item, index }) => {
  console.log('点击宫格菜单:', item, index)
  if (item.url) {
    // 这里可以使用 Vue Router 进行路由跳转
    // router.push(item.url)
    // 或者直接跳转
    window.location.href = item.url
  }
}

// 更多按钮点击处理
const handleMoreClick = () => {
  console.log('点击更多按钮')
  // 跳转到分类页面或显示更多菜单
  // router.push('/category')
}

// 获取爆款好物列表
const getGuessList = async (isLoadMore = false) => {
  if (loading.value) return

  loading.value = true

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: bfhwCurrentPage.value,
    page_size: bffhwPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_GUESS_GOODS_ID
  })

  if (!err && json && Array.isArray(json)) {
    // 转换数据格式为GoodsWaterfallItem组件需要的格式
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    if (isLoadMore) {
      guessList.value = [...guessList.value, ...newItems]
    } else {
      guessList.value = newItems
    }

    // 只有当返回的数据为空数组时才停止加载
    if (json.length === 0) {
      finished.value = true
    }
  } else {
    // 没有数据或出错时，标记为加载完成
    finished.value = true
  }

  loading.value = false
}

// 加载更多处理
const handleLoadMore = () => {
  if (!finished.value && !loading.value) {
    // 如果是初始加载，跳过（因为onMounted已经加载了第一页）
    if (isInitialLoad.value) {
      isInitialLoad.value = false
      return
    }

    bfhwCurrentPage.value++
    getGuessList(true)
  }
}

const handleGoodsClick = (goodsInfo) => {
  console.log('点击商品:', goodsInfo)
  if (goodsInfo.goodsId) {
    // 跳转到商品详情页
    router.push(`/goodsdetail/${goodsInfo.goodsId}`)
  }
}



// 组件挂载时获取数据
onMounted(() => {
  getHeaderBannerList()
  getIconList()
  getGuessList(false)
})
</script>

<style scoped lang="less">
.bf-home {
  .banner-container {
    margin: 8px 12px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .grid-menu-container {
    margin: 8px 0;
    background: #ffffff;
    border-radius: 12px;
    margin: 8px 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }


}
</style>
